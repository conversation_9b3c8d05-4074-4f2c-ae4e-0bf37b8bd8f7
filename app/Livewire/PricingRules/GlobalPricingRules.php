<?php

namespace App\Livewire\PricingRules;

use App\Models\PricingRules;
use App\Services\PricingRuleConflictService;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\On;
use Livewire\Component;

class GlobalPricingRules extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public PricingRules $record;

    // Modal properties
    public bool $showErrorModal = false;

    public string $modalTitle = '';

    // Combined conflicts property
    public array $combinedConflicts = [];

    // Track which fields have conflicts
    public bool $hasBaseFareConflicts = false;

    public bool $hasDistanceFareConflicts = false;

    // Track if notification was already shown
    private bool $notificationShown = false;

    public function mount(): void
    {
        // Get or create the pricing rules record
        $this->record = PricingRules::firstOrCreate(
            ['id' => 1],
            [
                'global_base_price' => 5.00,
                'global_price_per_km' => 5.00,
                'time_threshold_percentage' => 0.00,
            ]
        );

        $this->form->fill($this->record->attributesToArray());

        // Reset conflict flags
        $this->resetConflictFlags();
    }

    /**
     * Reset conflict flags
     */
    private function resetConflictFlags(): void
    {
        $this->hasBaseFareConflicts = false;
        $this->hasDistanceFareConflicts = false;
        $this->combinedConflicts = [];
        $this->notificationShown = false;
    }

    public function form(Form $form): Form
    {
        // Get current values for validation
        $currentGlobalBasePrice = $this->record->global_base_price;
        $currentGlobalPricePerKm = $this->record->global_price_per_km;

        return $form
            ->schema([
                Section::make('Global Rules')
                    ->description('Configure the foundational pricing rules that are universally applied to all rides. These rules determine the base fare, distance-based rates, and time threshold for acceptable deviations before penalties are enforced. Adjust these settings to reflect your global pricing strategy effectively.')
                    ->schema([
                        TextInput::make('global_base_price')
                            ->label('Base Fare (B)')
                            ->helperText('A fixed starting fee applied to all rides. Must be greater than 0.01 and at most 100.')
                            ->suffix('LYD')
                            ->extraInputAttributes([
                                'inputmode' => 'decimal',
                                'pattern' => '[0-9]*([.][0-9]{1,6})?', // Allows up to 6 decimal places
                                'maxlength' => 6, // Limit the number of characters
                                'oninput' => "this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');", // Allow only numbers and one decimal point
                            ])
                            ->rules([
                                'required',
                                'numeric',
                                'gt:0.01',
                                'lte:100',
                                function () use ($currentGlobalBasePrice, $currentGlobalPricePerKm) {
                                    return function (string $attribute, $value, \Closure $fail) use ($currentGlobalBasePrice, $currentGlobalPricePerKm) {
                                        // Only validate if the value is being decreased
                                        if ((float) $value < (float) $currentGlobalBasePrice) {
                                            $this->validatePricingConflicts($value, $currentGlobalPricePerKm, 'base', $fail);
                                        }
                                    };
                                },
                            ])
                            ->validationMessages([
                                'gt' => 'The base fare must be greater than 0.01.',
                                'lte' => 'The base fare must not exceed 100.',
                            ]),

                        TextInput::make('global_price_per_km')
                            ->label('Distance-Based Pricing (D)')
                            ->helperText('Rate applied per unit of distance traveled. Must be greater than 0.01 and at most 100.')
                            ->suffix('LYD')
                            ->extraInputAttributes([
                                'inputmode' => 'decimal',
                                'pattern' => '[0-9]*([.][0-9]{1,6})?', // Allows up to 6 decimal places
                                'maxlength' => 6,
                                'oninput' => "this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');",
                            ])
                            ->rules([
                                'required',
                                'numeric',
                                'gt:0.01',
                                'lte:100',
                                function () use ($currentGlobalBasePrice, $currentGlobalPricePerKm) {
                                    return function (string $attribute, $value, \Closure $fail) use ($currentGlobalBasePrice, $currentGlobalPricePerKm) {
                                        // Only validate if the value is being decreased
                                        if ((float) $value < (float) $currentGlobalPricePerKm) {
                                            $this->validatePricingConflicts($currentGlobalBasePrice, $value, 'distance', $fail);
                                        }
                                    };
                                },
                            ])
                            ->validationMessages([
                                'gt' => 'The distance-based price must be greater than 0.01.',
                                'lte' => 'The distance-based price must not exceed 100.',
                            ]),
                        TextInput::make('time_threshold_percentage')
                            ->label('Time Threshold (T)')
                            ->helperText('Acceptable deviation percentage for ride duration before penalties apply. Example: 20%')
                            ->suffix('%')
                            ->extraInputAttributes([
                                'inputmode' => 'decimal',
                                'pattern' => '[0-9.]*',
                                'maxlength' => 6,
                                'oninput' => "this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');",
                            ])
                            ->rules(['required', 'numeric', 'gte:0', 'lte:100'])
                            ->validationMessages([
                                'gte' => 'The time threshold must be at least 0%.',
                                'lte' => 'The time threshold must not exceed 100%.',
                            ]),
                    ])->columns(3),
            ])
            ->statePath('data')
            ->model($this->record);
    }

    /**
     * Check for combined conflicts when both base and distance fares are being reduced
     */
    private function checkCombinedConflicts(float $newBasePrice, float $newDistancePrice): array
    {
        $conflictService = new PricingRuleConflictService;

        return $conflictService->checkForConflicts($newBasePrice, $newDistancePrice);
    }

    /**
     * Unified validation method for pricing conflicts
     */
    private function validatePricingConflicts($newBasePrice, $newDistancePrice, string $changedField, \Closure $fail): void
    {
        // Reset flags if this is the first validation in this cycle
        if (! $this->notificationShown) {
            $this->resetConflictFlags();
        }

        // Check for combined conflicts
        $conflicts = $this->checkCombinedConflicts((float) $newBasePrice, (float) $newDistancePrice);

        if (! empty($conflicts)) {
            // Store conflicts for modal
            $this->combinedConflicts = $conflicts;

            // Determine which fields have conflicts by analyzing the conflict data
            $this->analyzeConflictTypes($conflicts);

            // Show a simpler message under the field
            $fail('Lowering the pricing will affect other components. See details for more information.');

            // Show unified notification only once
            if (! $this->notificationShown) {
                $this->showUnifiedConflictNotification();
                $this->notificationShown = true;
            }
        }
    }

    /**
     * Analyze conflicts to determine which fare types are affected
     */
    private function analyzeConflictTypes(array $conflicts): void
    {
        foreach ($conflicts as $componentType => $items) {
            foreach ($items as $item) {
                foreach ($item['conflicts'] as $conflictType => $conflictInfo) {
                    // Check for base fare conflicts (including variations)
                    if (str_contains($conflictType, 'base_fare') ||
                        str_contains($conflictType, 'day_base_fare') ||
                        str_contains($conflictType, 'night_base_fare') ||
                        $conflictType === 'base_fare') {
                        $this->hasBaseFareConflicts = true;
                    }

                    // Check for distance fare conflicts (including variations)
                    if (str_contains($conflictType, 'distance_fare') ||
                        str_contains($conflictType, 'day_distance_fare') ||
                        str_contains($conflictType, 'night_distance_fare') ||
                        $conflictType === 'distance_fare') {
                        $this->hasDistanceFareConflicts = true;
                    }
                }
            }
        }
    }

    /**
     * Show unified conflict notification
     */
    private function showUnifiedConflictNotification(): void
    {
        $conflictTypes = [];
        if ($this->hasBaseFareConflicts) {
            $conflictTypes[] = 'Base Fare (B)';
        }
        if ($this->hasDistanceFareConflicts) {
            $conflictTypes[] = 'Distance Fare (D)';
        }

        $typeText = implode(' and ', $conflictTypes);

        Notification::make()
            ->warning()
            ->title('Pricing Rule Conflicts')
            ->body("Lowering the {$typeText} will affect pricing in other components. Please review the details.")
            ->persistent()
            ->actions([
                \Filament\Notifications\Actions\Action::make('view-details')
                    ->button()
                    ->label('View Details')
                    ->color('warning')
                    ->close()
                    ->dispatch('show-error-modal'),
            ])
            ->send();
    }

    /**
     * Build tabbed error message from conflicts
     */
    private function buildTabbedErrorMessage(array $conflicts): string
    {
        if (empty($conflicts)) {
            return '<div class="text-center py-8 text-gray-500">No conflicts found.</div>';
        }

        // Separate conflicts by base fare and distance fare
        $baseFareConflicts = [];
        $distanceFareConflicts = [];

        foreach ($conflicts as $componentType => $items) {
            foreach ($items as $item) {
                $baseFareItem = null;
                $distanceFareItem = null;

                // Create separate items for base fare and distance fare conflicts
                foreach ($item['conflicts'] as $conflictType => $conflictInfo) {
                    // Check for base fare conflicts
                    if (str_contains($conflictType, 'base_fare') ||
                        str_contains($conflictType, 'day_base_fare') ||
                        str_contains($conflictType, 'night_base_fare') ||
                        $conflictType === 'base_fare') {

                        if ($baseFareItem === null) {
                            $baseFareItem = [
                                'id' => $item['id'],
                                'name' => $item['name'],
                                'conflicts' => [],
                            ];
                            // Copy other item properties if they exist
                            if (isset($item['seats_number'])) {
                                $baseFareItem['seats_number'] = $item['seats_number'];
                            }
                            if (isset($item['gender'])) {
                                $baseFareItem['gender'] = $item['gender'];
                            }
                        }
                        $baseFareItem['conflicts'][$conflictType] = $conflictInfo;
                    }

                    // Check for distance fare conflicts
                    if (str_contains($conflictType, 'distance_fare') ||
                        str_contains($conflictType, 'day_distance_fare') ||
                        str_contains($conflictType, 'night_distance_fare') ||
                        $conflictType === 'distance_fare') {

                        if ($distanceFareItem === null) {
                            $distanceFareItem = [
                                'id' => $item['id'],
                                'name' => $item['name'],
                                'conflicts' => [],
                            ];
                            // Copy other item properties if they exist
                            if (isset($item['seats_number'])) {
                                $distanceFareItem['seats_number'] = $item['seats_number'];
                            }
                            if (isset($item['gender'])) {
                                $distanceFareItem['gender'] = $item['gender'];
                            }
                        }
                        $distanceFareItem['conflicts'][$conflictType] = $conflictInfo;
                    }
                }

                // Add items to their respective arrays
                if ($baseFareItem !== null) {
                    if (! isset($baseFareConflicts[$componentType])) {
                        $baseFareConflicts[$componentType] = [];
                    }
                    $baseFareConflicts[$componentType][] = $baseFareItem;
                }

                if ($distanceFareItem !== null) {
                    if (! isset($distanceFareConflicts[$componentType])) {
                        $distanceFareConflicts[$componentType] = [];
                    }
                    $distanceFareConflicts[$componentType][] = $distanceFareItem;
                }
            }
        }

        // Only show tabs if both types have conflicts
        if (! empty($baseFareConflicts) && ! empty($distanceFareConflicts)) {
            $message = '<div x-data="{ activeTab: \'base\' }" class="w-full">';

            // Tab Navigation
            $message .= '<div class="border-b border-gray-200 dark:border-gray-700 mb-4">';
            $message .= '<nav class="-mb-px flex space-x-8">';

            $message .= '<button @click="activeTab = \'base\'" :class="activeTab === \'base\' ? \'border-warning-500 text-warning-600\' : \'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors">';
            $message .= 'Base Fare (B) Conflicts';
            $message .= '</button>';

            $message .= '<button @click="activeTab = \'distance\'" :class="activeTab === \'distance\' ? \'border-warning-500 text-warning-600\' : \'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\'" class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors">';
            $message .= 'Distance Fare (D) Conflicts';
            $message .= '</button>';

            $message .= '</nav>';
            $message .= '</div>';

            // Tab Content
            $message .= '<div x-show="activeTab === \'base\'" class="space-y-4">';
            $message .= $this->buildConflictContent($baseFareConflicts, 'base fare');
            $message .= '</div>';

            $message .= '<div x-show="activeTab === \'distance\'" class="space-y-4">';
            $message .= $this->buildConflictContent($distanceFareConflicts, 'distance fare');
            $message .= '</div>';

            $message .= '</div>';
        } else {
            // Show single content without tabs
            $message = '<div class="w-full">';

            if (! empty($baseFareConflicts)) {
                $message .= '<div class="space-y-4">';
                $message .= $this->buildConflictContent($baseFareConflicts, 'base fare');
                $message .= '</div>';
            } elseif (! empty($distanceFareConflicts)) {
                $message .= '<div class="space-y-4">';
                $message .= $this->buildConflictContent($distanceFareConflicts, 'distance fare');
                $message .= '</div>';
            }

            $message .= '</div>';
        }

        return $message;
    }

    /**
     * Build conflict content for a specific fare type
     */
    private function buildConflictContent(array $conflicts, string $fareType): string
    {
        $message = "<p class='font-medium text-warning-600 mb-4'>Lowering the global {$fareType} will result in pricing issues in the following components:</p>";

        $conflictTypes = [
            'areas' => 'Areas',
            'vehicle_types' => 'Vehicle Types',
            'seat_numbers' => 'Seat Configurations',
            'gender_rules' => 'Gender Rules',
            'equipment' => 'Equipment',
            'day_time_configs' => 'Day-Time Configurations',
        ];

        foreach ($conflictTypes as $key => $title) {
            if (! isset($conflicts[$key]) || empty($conflicts[$key])) {
                continue;
            }

            $message .= "<div class='mt-3'>";
            $message .= "<h3 class='text-sm font-semibold text-gray-900 dark:text-white mb-2'>Affected {$title}:</h3>";
            $message .= "<ul class='space-y-2 pl-0'>";

            foreach ($conflicts[$key] as $item) {
                $message .= "<li class='flex items-start'>";

                // Get item name
                if ($key === 'areas' || $key === 'vehicle_types' || $key === 'equipment' || $key === 'day_time_configs') {
                    $itemName = $item['name'];
                } elseif ($key === 'seat_numbers') {
                    $itemName = "{$item['seats_number']} seats";
                } elseif ($key === 'gender_rules') {
                    $itemName = $item['gender'];
                } else {
                    $itemName = 'Unknown';
                }

                $message .= "<div class='block'>";
                $message .= "<span class='font-medium'>{$itemName}</span>";

                // Show all conflicts for this item (they're already filtered by fare type)
                $conflictDetails = [];
                foreach ($item['conflicts'] as $conflictType => $conflictInfo) {
                    $currentValue = number_format((float) $conflictInfo['current_value'], 2);
                    $minAllowed = number_format((float) $conflictInfo['min_allowed'], 2);
                    $label = str_replace('_', ' ', ucfirst($conflictType));
                    $conflictDetails[] = "{$label}: <span class='text-danger-600'>{$currentValue} LYD</span> (min: <span class='text-success-600'>{$minAllowed} LYD</span>)";
                }

                if (! empty($conflictDetails)) {
                    $message .= "<br><span class='text-sm text-gray-600 dark:text-gray-400'>".implode(', ', $conflictDetails).'</span>';
                }

                $message .= '</div>';
                $message .= '</li>';
            }

            $message .= '</ul>';
            $message .= '</div>';
        }

        $message .= "<div class='mt-4 pt-3 border-t border-gray-200 dark:border-gray-700'>";
        $message .= "<p class='flex items-start'>";
        $message .= "<span class='text-xl mr-2'>👉</span> ";
        $message .= "<span>Please update the {$fareType} for the components mentioned above before reducing the global {$fareType}.</span>";
        $message .= '</p>';
        $message .= '</div>';

        return $message;
    }

    /**
     * Shows the error details modal with tabbed content
     */
    #[On('show-error-modal')]
    public function showErrorDetailsModal(): void
    {
        $conflictTypes = [];
        if ($this->hasBaseFareConflicts) {
            $conflictTypes[] = 'Base Fare (B)';
        }
        if ($this->hasDistanceFareConflicts) {
            $conflictTypes[] = 'Distance Fare (D)';
        }

        $this->modalTitle = 'Pricing Rule Conflicts - '.implode(' & ', $conflictTypes);
        $this->showErrorModal = true;
    }

    /**
     * Get the modal content with tabs
     */
    public function getModalContentProperty(): string
    {
        // Debug: Log the conflicts structure
        \Log::info('Combined Conflicts Structure:', $this->combinedConflicts);
        \Log::info('Has Base Fare Conflicts:', ['value' => $this->hasBaseFareConflicts]);
        \Log::info('Has Distance Fare Conflicts:', ['value' => $this->hasDistanceFareConflicts]);

        return $this->buildTabbedErrorMessage($this->combinedConflicts);
    }

    /**
     * Close the error details modal
     */
    public function closeErrorModal(): void
    {
        $this->showErrorModal = false;
    }

    /**
     * Saves the global pricing rules to the database.
     *
     * This method first formats the numbers before saving them and then updates the
     * pricing rules record with the new values. Finally, it refreshes the form with
     * the updated values and sends a success notification.
     */
    public function save(): void
    {
        $data = $this->form->getState();

        // Format numbers before saving - convert to float first to ensure proper handling
        $data['global_base_price'] = (float) $data['global_base_price'];
        $data['global_price_per_km'] = (float) $data['global_price_per_km'];
        $data['time_threshold_percentage'] = (float) $data['time_threshold_percentage'];

        $this->record->update($data);

        // Refresh form with updated values
        $this->form->fill($this->record->fresh()->attributesToArray());

        // Emit event to notify other components
        $this->dispatch('pricing-rules-updated');

        Notification::make()
            ->title('Update Successful')
            ->body('Global rules updated successfully.')
            ->success()
            ->send();
    }

    public function render(): View
    {
        return view('livewire.pricing-rules.global-pricing-rules');
    }
}
