<div>
    <form wire:submit.prevent="save" class="fi-form grid gap-y-6">
        {{ $this->form }}
        <div class="fi-form-actions">
            <div class="fi-ac gap-3 flex flex-wrap items-center justify-start">
                <button type="submit"
                    style="--c-400:var(--primary-400);--c-500:var(--primary-500);--c-600:var(--primary-600);"
                    class="fi-btn fi-custom-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-primary fi-color-primary fi-size-md fi-btn-size-md gap-1.5 px-3 py-2 text-sm inline-grid shadow-sm bg-custom-600 text-white hover:bg-custom-500 focus-visible:ring-custom-500/50 dark:bg-custom-500 dark:hover:bg-custom-400 dark:focus-visible:ring-custom-400/50 fi-ac-action fi-ac-btn-action"
                    wire:loading.attr="disabled" wire:target="save">
                    <div class="flex items-center gap-2">
                        <div wire:loading wire:target="save">
                            <x-filament::loading-indicator class="h-5 w-5" />
                        </div>
                        <span>Save changes</span>
                    </div>
                </button>
            </div>
        </div>
    </form>
    <x-filament-actions::modals />

    <!-- Error Details Modal -->
    @if($showErrorModal)
        <div x-data="{}" x-on:keydown.escape.window="$wire.closeErrorModal()" class="fixed inset-0 z-50 overflow-hidden"
            style="background-color: rgba(0, 0, 0, 0.5);">
            <div class="flex min-h-full items-center justify-center p-4">
                <div class="relative w-full max-w-4xl bg-white dark:bg-gray-800 rounded-xl shadow-xl"
                    style="max-height: 80vh; display: flex; flex-direction: column;">
                    <!-- Modal Header -->
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700" style="flex-shrink: 0;">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                {{ $modalTitle }}
                            </h3>
                            <button type="button" wire:click="closeErrorModal"
                                class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                <span class="sr-only">Close</span>
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Modal Content -->
                    <div class="px-6 py-4" style="flex: 1; overflow-y: auto; min-height: 0;">
                        <div class="text-sm text-gray-600 dark:text-gray-400 space-y-4">
                            {!! $this->modalContent !!}
                        </div>
                    </div>

                    <!-- Modal Footer -->
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700" style="flex-shrink: 0;">
                        <div class="flex justify-end">
                            <button type="button"
                                class="fi-btn relative inline-flex items-center justify-center font-medium outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-btn-size-md gap-1.5 px-3 py-2 text-sm fi-btn-color-gray bg-white text-gray-950 hover:bg-gray-50 dark:bg-white/5 dark:text-white dark:hover:bg-white/10 ring-1 ring-gray-950/10 dark:ring-white/20"
                                wire:click="closeErrorModal">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>